(function( $ ) {
	'use strict';

	/**
	 * All of the code for your public-facing JavaScript source
	 * should reside in this file.
	 *
	 * Note: It has been assumed you will write jQuery code here, so the
	 * $ function reference has been prepared for usage within the scope
	 * of this function.
	 *
	 * This enables you to define handlers, for when the DOM is ready:
	 *
	 * $(function() {
	 *
	 * });
	 *
	 * When the window is loaded:
	 *
	 * $( window ).load(function() {
	 *
	 * });
	 *
	 * ...and/or other possibilities.
	 *
	 * Ideally, it is not considered best practise to attach more than a
	 * single DOM-ready or window-load handler for a particular page.
	 * Although scripts in the WordPress core, Plugins and Themes may be
	 * practising this, we should strive to set a better example in our own work.
	 */

	/* After site is loaded...
	========================================================================= */

	$(window).load(function () {
		// setup_flex_slider();
		setup_definition_list_accordion();
	});

	/* Definition List Accordion
	========================================================================= */

	function setup_definition_list_accordion() {
		var $all_triggers = $('.js-issscr-accordion-trigger');
		var $all_targets = $('.js-issscr-accordion-target');

		$all_triggers.click(function(e) {
			// If panel is closed...
			if( $(this).parent().attr('data-status') == 'closed' ) {
				// Close all panels
				$all_targets.each(function( index ) {
					$(this).parent().attr('data-status', 'closed');
					$(this).slideUp();
				});
				// Open panel
				$(this).next().slideDown();
				$(this).parent().attr('data-status', 'open');
			}
			// If panel is open...
			else {
				// Close panel
				$(this).next().slideUp();
				$(this).parent().attr('data-status', 'closed');
			}

			e.preventDefault();
		});
	}

	/* Flex Slider
	========================================================================= */

	function setup_flex_slider() {
		if ($.isFunction(jQuery.fn.flexslider)) {

			var config = {
				namespace: "flex-",             //{NEW} String: Prefix string attached to the class of every element generated by the plugin
				selector: ".slides > li",       //{NEW} Selector: Must match a simple pattern. '{container} > {slide}' -- Ignore pattern at your own peril
				animation: "fade",              //String: Select your animation type, "fade" or "slide"
				easing: "swing",               //{NEW} String: Determines the easing method used in jQuery transitions. jQuery easing plugin is supported!
				direction: "horizontal",        //String: Select the sliding direction, "horizontal" or "vertical"
				reverse: false,                 //{NEW} Boolean: Reverse the animation direction
				animationLoop: true,             //Boolean: Should the animation loop? If false, directionNav will received "disable" classes at either end
				smoothHeight: false,            //{NEW} Boolean: Allow height of the slider to animate smoothly in horizontal mode
				startAt: 0,                     //Integer: The slide that the slider should start on. Array notation (0 = first slide)
				slideshow: true,                //Boolean: Animate slider automatically
				slideshowSpeed: 3000,           //Integer: Set the speed of the slideshow cycling, in milliseconds
				animationSpeed: 600,            //Integer: Set the speed of animations, in milliseconds
				initDelay: 0,                   //{NEW} Integer: Set an initialization delay, in milliseconds
				randomize: false,               //Boolean: Randomize slide order

				// Usability features
				pauseOnAction: true,            //Boolean: Pause the slideshow when interacting with control elements, highly recommended.
				pauseOnHover: false,            //Boolean: Pause the slideshow when hovering over slider, then resume when no longer hovering
				useCSS: true,                   //{NEW} Boolean: Slider will use CSS3 transitions if available
				touch: true,                    //{NEW} Boolean: Allow touch swipe navigation of the slider on touch-enabled devices
				video: false,                   //{NEW} Boolean: If using video in the slider, will prevent CSS3 3D Transforms to avoid graphical glitches

				// Primary Controls
				controlNav: false,               //Boolean: Create navigation for paging control of each clide? Note: Leave true for manualControls usage
				directionNav: false,             //Boolean: Create navigation for previous/next navigation? (true/false)
				prevText: "Previous",           //String: Set the text for the "previous" directionNav item
				nextText: "Next",               //String: Set the text for the "next" directionNav item

				// Secondary Navigation
				keyboard: true,                 //Boolean: Allow slider navigating via keyboard left/right keys
				multipleKeyboard: false,        //{NEW} Boolean: Allow keyboard navigation to affect multiple sliders. Default behavior cuts out keyboard navigation with more than one slider present.
				mousewheel: false,              //{UPDATED} Boolean: Requires jquery.mousewheel.js (https://github.com/brandonaaron/jquery-mousewheel) - Allows slider navigating via mousewheel
				pausePlay: false,               //Boolean: Create pause/play dynamic element
				pauseText: 'Pause',             //String: Set the text for the "pause" pausePlay item
				playText: 'Play',               //String: Set the text for the "play" pausePlay item

				// Special properties
				controlsContainer: "",          //{UPDATED} Selector: USE CLASS SELECTOR. Declare which container the navigation elements should be appended too. Default container is the FlexSlider element. Example use would be ".flexslider-container". Property is ignored if given element is not found.
				manualControls: "",             //Selector: Declare custom control navigation. Examples would be ".flex-control-nav li" or "#tabs-nav li img", etc. The number of elements in your controlNav should match the number of slides/tabs.
				sync: "",                       //{NEW} Selector: Mirror the actions performed on this slider with another slider. Use with care.
				asNavFor: "",                   //{NEW} Selector: Internal property exposed for turning the slider into a thumbnail navigation for another slider

				// Carousel Options
				itemWidth: 0,                   //{NEW} Integer: Box-model width of individual carousel items, including horizontal borders and padding.
				itemMargin: 0,                  //{NEW} Integer: Margin between carousel items.
				minItems: 0,                    //{NEW} Integer: Minimum number of carousel items that should be visible. Items will resize fluidly when below this.
				maxItems: 0,                    //{NEW} Integer: Maxmimum number of carousel items that should be visible. Items will resize fluidly when above this limit.
				move: 0,                        //{NEW} Integer: Number of carousel items that should move on animation. If 0, slider will move all visible items.

				// Callback API
				start: function (slider) {
					slider.container.click(function () {
						if (!slider.animating) {
							slider.flexAnimate(slider.getTarget('next'));
						}
					});
				},            //Callback: function(slider) - Fires when the slider loads the first slide
				before: function () {
				},           //Callback: function(slider) - Fires asynchronously with each slider animation
				after: function () {
				},            //Callback: function(slider) - Fires after each slider animation completes
				end: function () {
				},              //Callback: function(slider) - Fires when the slider reaches the last slide (asynchronous)
				added: function () {
				},            //{NEW} Callback: function(slider) - Fires after a slide is added
				removed: function () {
				}           //{NEW} Callback: function(slider) - Fires after a slide is removed
			}

			$('.flexslider').flexslider( config );
			config[slideshow] = false;
			$('.flexslider-no-auto-start').flexslider( config );

		}
	}

})( jQuery );
