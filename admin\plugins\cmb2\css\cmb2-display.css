/*!
 * CMB2 - v2.7.0 - 2020-01-21
 * https://cmb2.io
 * Copyright (c) 2020
 * Licensed GPLv2+
 */

/*--------------------------------------------------------------
 * CMB2 Display Styling
--------------------------------------------------------------*/
/* line 6, sass/partials/_display.scss */
.cmb2-colorpicker-swatch span {
  display: inline-block;
  width: 1em;
  height: 1em;
  border-radius: 1em;
  float: left;
  margin-top: 3px;
  margin-right: 2px;
}

/* line 17, sass/partials/_display.scss */
.cmb2-code {
  overflow: scroll;
}

/* line 21, sass/partials/_display.scss */
.cmb-image-display {
  max-width: 100%;
  height: auto;
}

/* line 26, sass/partials/_display.scss */
.cmb2-display-file-list li {
  display: inline;
  margin: 0 .5em .5em 0;
}

/* line 31, sass/partials/_display.scss */
.cmb2-oembed * {
  max-width: 100%;
  height: auto;
}

/*# sourceMappingURL=cmb2-display.css.map */
