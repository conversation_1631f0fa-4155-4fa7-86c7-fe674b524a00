# Copyright (C) 2016 WebDevStudios
# This file is distributed under the same license as the CMB2 package.
# Translators:
# <PERSON><PERSON><PERSON> <juerg.hun<PERSON><EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: CMB2\n"
"Report-Msgid-Bugs-To: http://wordpress.org/support/plugin/cmb2\n"
"POT-Creation-Date: 2016-06-27 17:01:22+00:00\n"
"PO-Revision-Date: 2016-06-27 17:01+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German (Switzerland) (http://www.transifex.com/wp-translations/cmb2/language/de_CH/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: de_CH\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: grunt-wp-i18n 0.4.9\n"
"X-Poedit-Basepath: ../\n"
"X-Poedit-Bookmarks: \n"
"X-Poedit-Country: United States\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c;\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"

#: example-functions.php:117 tests/test-cmb-field.php:255
msgid "Test Metabox"
msgstr "Test Metabox"

#: example-functions.php:130 example-functions.php:436
msgid "Test Text"
msgstr "Test Text"

#: example-functions.php:131 example-functions.php:144
#: example-functions.php:157 example-functions.php:165
#: example-functions.php:173 example-functions.php:182
#: example-functions.php:190 example-functions.php:205
#: example-functions.php:213 example-functions.php:221
#: example-functions.php:238 example-functions.php:247
#: example-functions.php:260 example-functions.php:267
#: example-functions.php:274 example-functions.php:288
#: example-functions.php:301 example-functions.php:314
#: example-functions.php:326 example-functions.php:335
#: example-functions.php:343 example-functions.php:352
#: example-functions.php:359 example-functions.php:373
#: example-functions.php:437 example-functions.php:528
#: example-functions.php:536 example-functions.php:543
#: example-functions.php:550 example-functions.php:557
#: example-functions.php:564 example-functions.php:571
#: example-functions.php:598 example-functions.php:606
#: example-functions.php:613 example-functions.php:650
#: tests/test-cmb-field.php:267
msgid "field description (optional)"
msgstr "Feld Beschreibung (optional)"

#: example-functions.php:143
msgid "Test Text Small"
msgstr "Test Text Klein"

#: example-functions.php:156
msgid "Test Text Medium"
msgstr "Test Text Mittel"

#: example-functions.php:164
msgid "Custom Rendered Field"
msgstr ""

#: example-functions.php:172
msgid "Website URL"
msgstr "Website URL"

#: example-functions.php:181
msgid "Test Text Email"
msgstr "Text Text Email"

#: example-functions.php:189
msgid "Test Time"
msgstr "Test Zeit"

#: example-functions.php:197 example-functions.php:198
msgid "Time zone"
msgstr "Zeitzone"

#: example-functions.php:204
msgid "Test Date Picker"
msgstr "Test Datumsauswahl"

#: example-functions.php:212
msgid "Test Date Picker (UNIX timestamp)"
msgstr "Test Datumsauswahl (UNIX timestamp)"

#: example-functions.php:220
msgid "Test Date/Time Picker Combo (UNIX timestamp)"
msgstr "Test Datums-/Zeitauswahl Kombination (UNIX timestamp)"

#: example-functions.php:237
msgid "Test Money"
msgstr "Test Geld mit Währungssymbol"

#: example-functions.php:246
msgid "Test Color Picker"
msgstr "Test Farbauswahl"

#: example-functions.php:259
msgid "Test Text Area"
msgstr "Test Textbereich"

#: example-functions.php:266
msgid "Test Text Area Small"
msgstr "Test Textbereich Klein"

#: example-functions.php:273
msgid "Test Text Area for Code"
msgstr "Test Textbereich für die Programmcode Eingabe"

#: example-functions.php:280
msgid "Test Title Weeeee"
msgstr "Test Titel Weeeee"

#: example-functions.php:281
msgid "This is a title description"
msgstr "Dies ist eine Titelbeschreibung"

#: example-functions.php:287
msgid "Test Select"
msgstr "Test Select"

#: example-functions.php:293 example-functions.php:306
#: example-functions.php:318
msgid "Option One"
msgstr "Option 1"

#: example-functions.php:294 example-functions.php:307
#: example-functions.php:319
msgid "Option Two"
msgstr "Option 2"

#: example-functions.php:295 example-functions.php:308
#: example-functions.php:320
msgid "Option Three"
msgstr "Option 3"

#: example-functions.php:300
msgid "Test Radio inline"
msgstr "Test Radio inline"

#: example-functions.php:313
msgid "Test Radio"
msgstr "Test Radio"

#: example-functions.php:325
msgid "Test Taxonomy Radio"
msgstr "Test Taxonomy Radio"

#: example-functions.php:334
msgid "Test Taxonomy Select"
msgstr "Test Taxonomy Select"

#: example-functions.php:342
msgid "Test Taxonomy Multi Checkbox"
msgstr "Test Taxonomy Mehrfach Checkbox"

#: example-functions.php:351
msgid "Test Checkbox"
msgstr "Test Checkbox"

#: example-functions.php:358 tests/test-cmb-field.php:266
msgid "Test Multi Checkbox"
msgstr "Test Mehrfach Checkbox"

#: example-functions.php:364 tests/test-cmb-field.php:272
msgid "Check One"
msgstr "1. Auswahl"

#: example-functions.php:365 tests/test-cmb-field.php:273
msgid "Check Two"
msgstr "2. Auswahl"

#: example-functions.php:366 tests/test-cmb-field.php:274
msgid "Check Three"
msgstr "3. Auswahl"

#: example-functions.php:372
msgid "Test wysiwyg"
msgstr "Test wysiwyg"

#: example-functions.php:380
msgid "Test Image"
msgstr "Test Bild"

#: example-functions.php:381
msgid "Upload an image or enter a URL."
msgstr "Lade ein Bild hoch, oder gib eine URL ein."

#: example-functions.php:387
msgid "Multiple Files"
msgstr "Mehrere Dateien"

#: example-functions.php:388
msgid "Upload or add multiple images/attachments."
msgstr "Hochladen oder mehrfaches hinzufügen von Bildern/Anhängen"

#: example-functions.php:395
msgid "oEmbed"
msgstr "oEmbed"

#: example-functions.php:396
msgid ""
"Enter a youtube, twitter, or instagram URL. Supports services listed at <a "
"href=\"http://codex.wordpress.org/Embeds\">http://codex.wordpress.org/Embeds</a>."
msgstr "Eingabe einer YouTube-, Twitter- oder Instagram-URL. Unterstützt alle Dienste welche auf <a href=\"http://codex.wordpress.org/Embeds\">http://codex.wordpress.org/Embeds</a> aufgelistet werden."

#: example-functions.php:427
msgid "About Page Metabox"
msgstr "Über die Metabox Seite"

#: example-functions.php:456
msgid "Repeating Field Group"
msgstr "Duplizierbare Feldgruppe"

#: example-functions.php:464
msgid "Generates reusable form entries"
msgstr "Erzeugt wiederverwendbare Formulareinträge"

#: example-functions.php:466
msgid "Entry {#}"
msgstr "Eintrag {#}"

#: example-functions.php:467
msgid "Add Another Entry"
msgstr "Füge einen weiteren Eintrag hinzu"

#: example-functions.php:468
msgid "Remove Entry"
msgstr "Eintrag entfernen"

#: example-functions.php:481
msgid "Entry Title"
msgstr "Eintragstitel"

#: example-functions.php:488
msgid "Description"
msgstr "Beschreibung"

#: example-functions.php:489
msgid "Write a short description for this entry"
msgstr "Schreibe eine kurze Beschreibung zu diesem Eintrag"

#: example-functions.php:495
msgid "Entry Image"
msgstr "Eintragsbild"

#: example-functions.php:501
msgid "Image Caption"
msgstr "Bildbeschreibung"

#: example-functions.php:520
msgid "User Profile Metabox"
msgstr "Benutzerprofil Metabox"

#: example-functions.php:527 example-functions.php:597
msgid "Extra Info"
msgstr "Zusätzliche Information"

#: example-functions.php:535
msgid "Avatar"
msgstr "Avatar"

#: example-functions.php:542
msgid "Facebook URL"
msgstr "Facebook URL"

#: example-functions.php:549
msgid "Twitter URL"
msgstr "Twitter URL"

#: example-functions.php:556
msgid "Google+ URL"
msgstr "Google+ URL"

#: example-functions.php:563
msgid "Linkedin URL"
msgstr "Linkedin URL"

#: example-functions.php:570
msgid "User Field"
msgstr "Benutzerdefiniertes Feld"

#: example-functions.php:590
msgid "Category Metabox"
msgstr ""

#: example-functions.php:605
msgid "Term Image"
msgstr ""

#: example-functions.php:612
msgid "Arbitrary Term Field"
msgstr ""

#: example-functions.php:634
msgid "Theme Options Metabox"
msgstr "Theme Optionen Metabox"

#: example-functions.php:649
msgid "Site Background Color"
msgstr "Seiten Hintergrundfarbe"

#: includes/CMB2.php:129
msgid "Metabox configuration is required to have an ID parameter"
msgstr "Bei der Metabox Konfiguration ist die Eingabe einer ID erforderlich"

#: includes/CMB2.php:418
msgid "Click to toggle"
msgstr "Zum Umschalten klicken"

#: includes/CMB2_Ajax.php:71
msgid "Please Try Again"
msgstr "Bitte versuche es erneut"

#: includes/CMB2_Ajax.php:173 tests/cmb-tests-base.php:59
msgid "Remove Embed"
msgstr "Eingebettete Datei entfernen"

#: includes/CMB2_Ajax.php:177 tests/cmb-tests-base.php:64
msgid "No oEmbed Results Found for %s. View more info at"
msgstr "Keine oEmbed Einträge für %s gefunden. Weitere Infos unter"

#: includes/CMB2_Field.php:1186
msgid "Add Group"
msgstr "Gruppe hinzufügen"

#: includes/CMB2_Field.php:1187
msgid "Remove Group"
msgstr "Gruppe entfernen"

#: includes/CMB2_Field.php:1209 includes/CMB2_Field.php:1213
#: tests/test-cmb-field.php:229
msgid "None"
msgstr "Keine"

#: includes/CMB2_Field.php:1269
msgid "Sorry, this field does not have a cmb_id specified."
msgstr ""

#: includes/CMB2_Field_Display.php:408 includes/CMB2_JS.php:139
#: includes/types/CMB2_Type_File_Base.php:75 tests/test-cmb-types-base.php:143
#: tests/test-cmb-types.php:701
msgid "File:"
msgstr "Datei:"

#: includes/CMB2_JS.php:86 includes/CMB2_JS.php:119
msgid "Clear"
msgstr "Leeren"

#: includes/CMB2_JS.php:87
msgid "Default"
msgstr "Standard"

#: includes/CMB2_JS.php:88
msgid "Select Color"
msgstr "Farbe wählen"

#: includes/CMB2_JS.php:89
msgid "Current Color"
msgstr "Aktuelle Farbe"

#: includes/CMB2_JS.php:110
msgid "Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday"
msgstr "Sonntag, Montag, Dienstag, Mittwoch, Donnerstag, Freitag, Samstag"

#: includes/CMB2_JS.php:111
msgid "Su, Mo, Tu, We, Th, Fr, Sa"
msgstr "So, Mo, Di, Mi, Do, Fr, Sa"

#: includes/CMB2_JS.php:112
msgid "Sun, Mon, Tue, Wed, Thu, Fri, Sat"
msgstr "Son, Mon, Die, Mit, Don, Fre, Sam"

#: includes/CMB2_JS.php:113
msgid ""
"January, February, March, April, May, June, July, August, September, "
"October, November, December"
msgstr "Januar, Februar, März, April, Mai, Juni, Juli, August, September, Oktober, November, Dezember"

#: includes/CMB2_JS.php:114
msgid "Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec"
msgstr "Jan, Feb, Mär, Apr, Mai, Jun, Jul, Aug, Sep, Okt, Nov, Dez"

#: includes/CMB2_JS.php:115
msgid "Next"
msgstr "Nächster"

#: includes/CMB2_JS.php:116
msgid "Prev"
msgstr "Vorheriger"

#: includes/CMB2_JS.php:117
msgid "Today"
msgstr "Heute"

#: includes/CMB2_JS.php:118 includes/CMB2_JS.php:128
msgid "Done"
msgstr "Fertig"

#: includes/CMB2_JS.php:122
msgid "Choose Time"
msgstr "Zeit auswählen"

#: includes/CMB2_JS.php:123
msgid "Time"
msgstr "Zeit"

#: includes/CMB2_JS.php:124
msgid "Hour"
msgstr "Stunde"

#: includes/CMB2_JS.php:125
msgid "Minute"
msgstr "Minute"

#: includes/CMB2_JS.php:126
msgid "Second"
msgstr "Sekunde"

#: includes/CMB2_JS.php:127
msgid "Now"
msgstr "Jetzt"

#: includes/CMB2_JS.php:135
msgid "Use this file"
msgstr "Verwende diese Datei"

#: includes/CMB2_JS.php:136
msgid "Use these files"
msgstr "Verwende diese Dateien"

#: includes/CMB2_JS.php:137 includes/types/CMB2_Type_File_Base.php:61
msgid "Remove Image"
msgstr "Bild entfernen"

#: includes/CMB2_JS.php:138 includes/CMB2_Types.php:257
#: includes/types/CMB2_Type_File_Base.php:80 tests/test-cmb-types-base.php:143
#: tests/test-cmb-types.php:47 tests/test-cmb-types.php:55
#: tests/test-cmb-types.php:701
msgid "Remove"
msgstr "Entfernen"

#: includes/CMB2_JS.php:140 includes/types/CMB2_Type_File_Base.php:78
#: tests/test-cmb-types-base.php:143 tests/test-cmb-types.php:701
msgid "Download"
msgstr "Download"

#: includes/CMB2_JS.php:141
msgid "Select / Deselect All"
msgstr "Alle auswählen / abwählen"

#: includes/CMB2_Types.php:194
msgid "Add Row"
msgstr "Zeile hinzufügen"

#: includes/CMB2_hookup.php:145
msgid ""
"Term Metadata is a WordPress > 4.4 feature. Please upgrade your WordPress "
"install."
msgstr ""

#: includes/CMB2_hookup.php:149
msgid "Term metaboxes configuration requires a 'taxonomies' parameter"
msgstr ""

#: includes/helper-functions.php:93
msgid "No oEmbed Results Found for %s. View more info at %s"
msgstr ""

#: includes/helper-functions.php:279
msgid "Save"
msgstr "Speichern"

#: includes/types/CMB2_Type_File.php:36 tests/test-cmb-types.php:683
#: tests/test-cmb-types.php:701
msgid "Add or Upload File"
msgstr "Datei hinzufügen oder hochladen"

#: includes/types/CMB2_Type_File_List.php:36 tests/test-cmb-types.php:639
#: tests/test-cmb-types.php:663
msgid "Add or Upload Files"
msgstr "Dateien hinzufügen oder hochladen"

#: includes/types/CMB2_Type_Taxonomy_Multicheck.php:27
#: includes/types/CMB2_Type_Taxonomy_Radio.php:25
msgid "No terms"
msgstr "Keine Einträge"

#. Plugin Name of the plugin/theme
msgid "CMB2"
msgstr "CMB2"

#. Plugin URI of the plugin/theme
msgid "https://github.com/CMB2/CMB2"
msgstr "https://github.com/CMB2/CMB2"

#. Description of the plugin/theme
msgid ""
"CMB2 will create metaboxes and forms with custom fields that will blow your "
"mind."
msgstr "CMB2 erstellt Metaboxen und Formulare mit benutzerdefinierten Feldern, die dich begeistern werden."

#. Author of the plugin/theme
msgid "WebDevStudios"
msgstr "WebDevStudios"

#. Author URI of the plugin/theme
msgid "http://webdevstudios.com"
msgstr "http://webdevstudios.com"

#: includes/CMB2_JS.php:109
msgctxt "Valid formatDate string for jquery-ui datepicker"
msgid "mm/dd/yy"
msgstr "dd.mm.yy"

#: includes/CMB2_JS.php:129
msgctxt ""
"Valid formatting string, as per "
"http://trentrichardson.com/examples/timepicker/"
msgid "hh:mm TT"
msgstr "HH:mm"
