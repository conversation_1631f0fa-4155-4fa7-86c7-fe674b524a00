=== SEO Content Randomizer ===
Contributors: intellasoftsolutions, freemius
Tags: SEO, Content, Text, Random, Randomization, Randomizing, Dynamic Content
Requires at least: 4.9.0
Tested up to: 6.2.0
Requires PHP: 5.6
Stable tag: 3.28.1
License: GPLv3
License URI: http://www.gnu.org/licenses/gpl.html

Write multiple versions of a page’s content that will be randomly selected each time the page is loaded. This also works with images and keywords.


== Description ==
The SEO Content Randomizer lets you write multiple versions of a post/page's content and will then randomly select one version each time the page is loaded. This also works with images and keywords. You can add multiple images or even multiple categories of images that you can add to your content with the use of shortcodes. When the page gets rendered it will randomly select one image per shortcode. The same goes for keywords, where you can define singular and plural keywords and insert a keyword shortcode into your content that will be replaced with a randomly selected one upon loading the page.

https://www.youtube.com/watch?v=YH2p9tD__QU

*[See a demo here](https://intellasoftsolutions.net/lp/flood-cleanup-in-troy-michigan/)*


== Features ==
- Create multiple versions of your content, images, and keywords, one randomly selected each time the page loads
- Create multiple content panels per post type
- Create multiple image panels per post type
- Create multiple keyword panels per post type
- Create a custom meta descriptions per page
- Function to pin specific content block for review
- Extensive settings panel


== Premium Features ==
The [Pro](https://intellasoftplugins.com/seo-content-randomizer/) and [Enterprise](https://intellasoftplugins.com/seo-content-randomizer/) versions of the plugin allow you to add more than just 3 content, image, and keyword blocks.

-  Get the [SEO Content Randomizer Pro version here](https://intellasoftplugins.com/seo-content-randomizer/) to be able create **10 random content**, **20 random image**, and **10 random keyword** blocks.
-  Get the [SEO Content Randomizer Enterprise version here](https://intellasoftplugins.com/seo-content-randomizer/) to be able create **unlimited random content**, **random image**, and **random keyword** blocks.

One-on-one email support is available to people who purchase the Pro or Enterprise version of the plugin.


== Affiliate Marketing ==
If you like the plugin, check out our [Affiliate Marketing Program](https://intellasoftplugins.com/affiliate/) for the Pro and Enterprise version.


== Frequently Asked Questions ==
= Will my original content get deleted, once I turn a page into a randomizer page? =

No. The plugin doesn't delete any of your data. If you turn a randomizer page back into a normal page, your original content, including the default content editor will appear again. You can even bring the default editor back, on randomizer pages, by going to "SEO Content Randomizer" in your WordPress Admin Dashboard and deactivating the setting "Hide Default Content Editor".

= Why is the default content editor hidden on randomizer pages and how do I get it back? =

Since the SEO Content Randomizer replaces your single editor with multiple content block editors, there is really no need to show the default editor, so we hide it by default. However, no content gets deleted and you can bring the default editor back by going to "SEO Content Randomizer" in your WordPress Admin Dashboard and deactivating the setting "Hide Default Content Editor".


== Installation ==
= From within WordPress =

1. In the admin dashboard, go to "Plugins > Add New".
1. Search for "SEO Content Randomizer".
1. Install and activate the plugin.

= Manually =
1. Download the plugin.
1. Login to your WordPress site’s admin dashboard.
1. In the admin dashboard, go to "Plugins > Add New".
1. Click the "Upload Plugin" button at the top of the page.
1. Click the "Choose File" button and select the plugin file you’ve just downloaded.
1. Click the "Install Now" button.
1. Once the plugin is installed successfully, click the "Activate Plugin" button.


== Further Reading ==
- [Read the Documentation](https://intellasoftplugins.com/docs/seo-content-randomizer/)
- [Read the FAQ](https://intellasoftplugins.com/seo-content-randomizer/#faq)


== Screenshots ==
1. Add multiple content blocks that will be randomly selected when the page loads
2. Add multiple images that will be randomly selected when the page loads
3. Adjust the settings for each post type individually