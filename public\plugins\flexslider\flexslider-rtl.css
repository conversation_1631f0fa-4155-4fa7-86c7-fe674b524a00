/*
 * jQuery FlexSlider v2.7.0
 * http://www.woothemes.com/flexslider/
 *
 * Copyright 2012 WooThemes
 * Free to use under the GPLv2 and later license.
 * http://www.gnu.org/licenses/gpl-2.0.html
 *
 * Contributing author: <PERSON> (@mbmufffin)
 *
 */
/* ====================================================================================================================
 * FONT-FACE
 * ====================================================================================================================*/

/* ====================================================================================================================
 * RESETS
 * ====================================================================================================================*/

/* ====================================================================================================================
 * BASE STYLES
 * ====================================================================================================================*/
.flexslider {
	direction:rtl;
}
/* ====================================================================================================================
 * DEFAULT THEME
 * ====================================================================================================================*/

.carousel li {
  margin-right: 5px;
}
.flex-direction-nav {
  *height: 0;
}

.flex-direction-nav a:before {
  content: '\f002';
}
.flex-direction-nav a.flex-next:before {
  content: '\f001';
}
.flex-direction-nav .flex-prev {
  left: auto; right: -50px;
}
.flex-direction-nav .flex-next {
  right: auto; left: -50px;
  text-align: left;
}
.flexslider:hover .flex-direction-nav .flex-prev {
  opacity: 0.7;
  left: auto; right: 10px;
}

.flexslider:hover .flex-direction-nav .flex-next {
  opacity: 0.7;
  right: auto; left: 10px;
}


.flex-pauseplay a {
  left: auto; right: 10px;
}


.flex-control-thumbs li {
  float: right;
}

/* ====================================================================================================================
 * RESPONSIVE
 * ====================================================================================================================*/
@media screen and (max-width: 860px) {
  .flex-direction-nav .flex-prev {
    left: auto; right: 10px;
  }
  .flex-direction-nav .flex-next {
    right: auto; left: 10px;
  }
}
