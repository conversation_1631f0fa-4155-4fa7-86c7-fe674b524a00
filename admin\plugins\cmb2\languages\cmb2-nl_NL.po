# Copyright (C) 2016 WebDevStudios
# This file is distributed under the same license as the CMB2 package.
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: CMB2\n"
"Report-Msgid-Bugs-To: http://wordpress.org/support/plugin/cmb2\n"
"POT-Creation-Date: 2016-06-27 17:01:22+00:00\n"
"PO-Revision-Date: 2016-06-27 17:01+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Dutch (Netherlands) (http://www.transifex.com/wp-translations/cmb2/language/nl_NL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nl_NL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: grunt-wp-i18n 0.4.9\n"
"X-Poedit-Basepath: ../\n"
"X-Poedit-Bookmarks: \n"
"X-Poedit-Country: United States\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c;\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"

#: example-functions.php:117 tests/test-cmb-field.php:255
msgid "Test Metabox"
msgstr "Test Metabox"

#: example-functions.php:130 example-functions.php:436
msgid "Test Text"
msgstr "Test Tekst"

#: example-functions.php:131 example-functions.php:144
#: example-functions.php:157 example-functions.php:165
#: example-functions.php:173 example-functions.php:182
#: example-functions.php:190 example-functions.php:205
#: example-functions.php:213 example-functions.php:221
#: example-functions.php:238 example-functions.php:247
#: example-functions.php:260 example-functions.php:267
#: example-functions.php:274 example-functions.php:288
#: example-functions.php:301 example-functions.php:314
#: example-functions.php:326 example-functions.php:335
#: example-functions.php:343 example-functions.php:352
#: example-functions.php:359 example-functions.php:373
#: example-functions.php:437 example-functions.php:528
#: example-functions.php:536 example-functions.php:543
#: example-functions.php:550 example-functions.php:557
#: example-functions.php:564 example-functions.php:571
#: example-functions.php:598 example-functions.php:606
#: example-functions.php:613 example-functions.php:650
#: tests/test-cmb-field.php:267
msgid "field description (optional)"
msgstr "Veldomschrijving (optioneel)"

#: example-functions.php:143
msgid "Test Text Small"
msgstr "Test tekst klein"

#: example-functions.php:156
msgid "Test Text Medium"
msgstr "Test tekst medium"

#: example-functions.php:164
msgid "Custom Rendered Field"
msgstr ""

#: example-functions.php:172
msgid "Website URL"
msgstr ""

#: example-functions.php:181
msgid "Test Text Email"
msgstr "Test tekst e-mail"

#: example-functions.php:189
msgid "Test Time"
msgstr "Test Tijd"

#: example-functions.php:197 example-functions.php:198
msgid "Time zone"
msgstr "Tijdzone"

#: example-functions.php:204
msgid "Test Date Picker"
msgstr "Test Datumprikker"

#: example-functions.php:212
msgid "Test Date Picker (UNIX timestamp)"
msgstr "Test Datumprikker (UNIX timestamp)"

#: example-functions.php:220
msgid "Test Date/Time Picker Combo (UNIX timestamp)"
msgstr "Test datum/tijdprikker combinatie (Unix timestamp)"

#: example-functions.php:237
msgid "Test Money"
msgstr "Test Geld"

#: example-functions.php:246
msgid "Test Color Picker"
msgstr "Test Kleurenkiezer"

#: example-functions.php:259
msgid "Test Text Area"
msgstr "Test tekst gebied"

#: example-functions.php:266
msgid "Test Text Area Small"
msgstr "Test tekst gebied klein"

#: example-functions.php:273
msgid "Test Text Area for Code"
msgstr "Test tekst gebied voor code"

#: example-functions.php:280
msgid "Test Title Weeeee"
msgstr "Test titel Weeee"

#: example-functions.php:281
msgid "This is a title description"
msgstr "Dit is een titel beschrijving"

#: example-functions.php:287
msgid "Test Select"
msgstr "Test selectie"

#: example-functions.php:293 example-functions.php:306
#: example-functions.php:318
msgid "Option One"
msgstr "Keuze Een"

#: example-functions.php:294 example-functions.php:307
#: example-functions.php:319
msgid "Option Two"
msgstr "Keuze Twee"

#: example-functions.php:295 example-functions.php:308
#: example-functions.php:320
msgid "Option Three"
msgstr "Keuze Drie"

#: example-functions.php:300
msgid "Test Radio inline"
msgstr "Test radio inline"

#: example-functions.php:313
msgid "Test Radio"
msgstr "TEst radio"

#: example-functions.php:325
msgid "Test Taxonomy Radio"
msgstr ""

#: example-functions.php:334
msgid "Test Taxonomy Select"
msgstr ""

#: example-functions.php:342
msgid "Test Taxonomy Multi Checkbox"
msgstr "Test Taxonomy multi checkbox"

#: example-functions.php:351
msgid "Test Checkbox"
msgstr ""

#: example-functions.php:358 tests/test-cmb-field.php:266
msgid "Test Multi Checkbox"
msgstr ""

#: example-functions.php:364 tests/test-cmb-field.php:272
msgid "Check One"
msgstr "Check een"

#: example-functions.php:365 tests/test-cmb-field.php:273
msgid "Check Two"
msgstr "Check twee"

#: example-functions.php:366 tests/test-cmb-field.php:274
msgid "Check Three"
msgstr "Check drie"

#: example-functions.php:372
msgid "Test wysiwyg"
msgstr ""

#: example-functions.php:380
msgid "Test Image"
msgstr "Test Afbeelding"

#: example-functions.php:381
msgid "Upload an image or enter a URL."
msgstr "Upload een afbeelding of voer een URL in."

#: example-functions.php:387
msgid "Multiple Files"
msgstr "Meerdere Bestanden"

#: example-functions.php:388
msgid "Upload or add multiple images/attachments."
msgstr "Uplaod of voeg meerdere beelden/bijlagen toe."

#: example-functions.php:395
msgid "oEmbed"
msgstr "oEmbed"

#: example-functions.php:396
msgid ""
"Enter a youtube, twitter, or instagram URL. Supports services listed at <a "
"href=\"http://codex.wordpress.org/Embeds\">http://codex.wordpress.org/Embeds</a>."
msgstr ""

#: example-functions.php:427
msgid "About Page Metabox"
msgstr "Over pagina Metabox"

#: example-functions.php:456
msgid "Repeating Field Group"
msgstr "Veldgroep herhalen"

#: example-functions.php:464
msgid "Generates reusable form entries"
msgstr "Genereer herbruikbare formulier toevoegingen"

#: example-functions.php:466
msgid "Entry {#}"
msgstr "Bericht {#}"

#: example-functions.php:467
msgid "Add Another Entry"
msgstr "Voeg nog een bericht toe"

#: example-functions.php:468
msgid "Remove Entry"
msgstr "Verwijder bericht"

#: example-functions.php:481
msgid "Entry Title"
msgstr "Bericht titel"

#: example-functions.php:488
msgid "Description"
msgstr "Omschrijving"

#: example-functions.php:489
msgid "Write a short description for this entry"
msgstr "Schrijf een korte beschrijving voor dit bericht"

#: example-functions.php:495
msgid "Entry Image"
msgstr "Bericht afbeelding"

#: example-functions.php:501
msgid "Image Caption"
msgstr "Onderschrift"

#: example-functions.php:520
msgid "User Profile Metabox"
msgstr "Gebruikersprofiel Metabox"

#: example-functions.php:527 example-functions.php:597
msgid "Extra Info"
msgstr "Extra Info"

#: example-functions.php:535
msgid "Avatar"
msgstr "Avatar"

#: example-functions.php:542
msgid "Facebook URL"
msgstr "Facebook URL"

#: example-functions.php:549
msgid "Twitter URL"
msgstr "Twitter URL"

#: example-functions.php:556
msgid "Google+ URL"
msgstr "Google+ URL"

#: example-functions.php:563
msgid "Linkedin URL"
msgstr "Linkedin URL"

#: example-functions.php:570
msgid "User Field"
msgstr "Gebruiker Veld"

#: example-functions.php:590
msgid "Category Metabox"
msgstr "Categorie Metabox"

#: example-functions.php:605
msgid "Term Image"
msgstr "Term afbeelding"

#: example-functions.php:612
msgid "Arbitrary Term Field"
msgstr ""

#: example-functions.php:634
msgid "Theme Options Metabox"
msgstr "Thema instellingen Metabox"

#: example-functions.php:649
msgid "Site Background Color"
msgstr "Achtergrondkleur Site"

#: includes/CMB2.php:129
msgid "Metabox configuration is required to have an ID parameter"
msgstr ""

#: includes/CMB2.php:418
msgid "Click to toggle"
msgstr "Klik om te wisselen"

#: includes/CMB2_Ajax.php:71
msgid "Please Try Again"
msgstr "Opnieuw proberen."

#: includes/CMB2_Ajax.php:173 tests/cmb-tests-base.php:59
msgid "Remove Embed"
msgstr "Verwijder Embed"

#: includes/CMB2_Ajax.php:177 tests/cmb-tests-base.php:64
msgid "No oEmbed Results Found for %s. View more info at"
msgstr "Geen oEmbeds gevonden voor %s. Bekijk meer info over"

#: includes/CMB2_Field.php:1186
msgid "Add Group"
msgstr "Voeg Groep Toe"

#: includes/CMB2_Field.php:1187
msgid "Remove Group"
msgstr "Verwijder Groep"

#: includes/CMB2_Field.php:1209 includes/CMB2_Field.php:1213
#: tests/test-cmb-field.php:229
msgid "None"
msgstr "Geen"

#: includes/CMB2_Field.php:1269
msgid "Sorry, this field does not have a cmb_id specified."
msgstr "Sorry, dit veld heeft geen cmg_id."

#: includes/CMB2_Field_Display.php:408 includes/CMB2_JS.php:139
#: includes/types/CMB2_Type_File_Base.php:75 tests/test-cmb-types-base.php:143
#: tests/test-cmb-types.php:701
msgid "File:"
msgstr "Bestand:"

#: includes/CMB2_JS.php:86 includes/CMB2_JS.php:119
msgid "Clear"
msgstr "Wissen"

#: includes/CMB2_JS.php:87
msgid "Default"
msgstr "Standaard"

#: includes/CMB2_JS.php:88
msgid "Select Color"
msgstr "Kleur selecteren"

#: includes/CMB2_JS.php:89
msgid "Current Color"
msgstr "Huidige kleur"

#: includes/CMB2_JS.php:110
msgid "Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday"
msgstr "Zondag, Maandag, Dinsdag, Woensdag, Donderdag, Vrijdag, Zaterdag, Zondag"

#: includes/CMB2_JS.php:111
msgid "Su, Mo, Tu, We, Th, Fr, Sa"
msgstr "Zo, Ma, Di, Wo, Do, Vr, Za"

#: includes/CMB2_JS.php:112
msgid "Sun, Mon, Tue, Wed, Thu, Fri, Sat"
msgstr "Zon, Maa, Din, Woe, Don, Vri, Zat"

#: includes/CMB2_JS.php:113
msgid ""
"January, February, March, April, May, June, July, August, September, "
"October, November, December"
msgstr "Januari, Februari, Maart, April, Mei, Juni, Juli, Augustus, September, Oktober, November, December"

#: includes/CMB2_JS.php:114
msgid "Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec"
msgstr "Jan, Feb, Maa, Apr, Mei, Jun, Jul, Aug, Sep, Okt, Nov, Dec"

#: includes/CMB2_JS.php:115
msgid "Next"
msgstr "Volgende"

#: includes/CMB2_JS.php:116
msgid "Prev"
msgstr "Vorige"

#: includes/CMB2_JS.php:117
msgid "Today"
msgstr "Vandaag"

#: includes/CMB2_JS.php:118 includes/CMB2_JS.php:128
msgid "Done"
msgstr "Klaar"

#: includes/CMB2_JS.php:122
msgid "Choose Time"
msgstr "Kies een tijd"

#: includes/CMB2_JS.php:123
msgid "Time"
msgstr "Tijd"

#: includes/CMB2_JS.php:124
msgid "Hour"
msgstr "Uur"

#: includes/CMB2_JS.php:125
msgid "Minute"
msgstr "Minuut"

#: includes/CMB2_JS.php:126
msgid "Second"
msgstr "Seconde"

#: includes/CMB2_JS.php:127
msgid "Now"
msgstr "Nu"

#: includes/CMB2_JS.php:135
msgid "Use this file"
msgstr "Gebruik dit bestand"

#: includes/CMB2_JS.php:136
msgid "Use these files"
msgstr "Gebruik deze bestanden"

#: includes/CMB2_JS.php:137 includes/types/CMB2_Type_File_Base.php:61
msgid "Remove Image"
msgstr "Afbeelding verwijderen"

#: includes/CMB2_JS.php:138 includes/CMB2_Types.php:257
#: includes/types/CMB2_Type_File_Base.php:80 tests/test-cmb-types-base.php:143
#: tests/test-cmb-types.php:47 tests/test-cmb-types.php:55
#: tests/test-cmb-types.php:701
msgid "Remove"
msgstr "Verwijderen"

#: includes/CMB2_JS.php:140 includes/types/CMB2_Type_File_Base.php:78
#: tests/test-cmb-types-base.php:143 tests/test-cmb-types.php:701
msgid "Download"
msgstr "Downloaden"

#: includes/CMB2_JS.php:141
msgid "Select / Deselect All"
msgstr "Alle selecteren / deselecteren"

#: includes/CMB2_Types.php:194
msgid "Add Row"
msgstr "Voeg Rij Toe"

#: includes/CMB2_hookup.php:145
msgid ""
"Term Metadata is a WordPress > 4.4 feature. Please upgrade your WordPress "
"install."
msgstr "Term Metadata is een Wordpress > 4.4 optie. Upgrade je WordPress alstublieft"
"installeren."

#: includes/CMB2_hookup.php:149
msgid "Term metaboxes configuration requires a 'taxonomies' parameter"
msgstr ""

#: includes/helper-functions.php:93
msgid "No oEmbed Results Found for %s. View more info at %s"
msgstr "Geen oEmbeds gevonden voor %s. Bekijk meer info over %s"

#: includes/helper-functions.php:279
msgid "Save"
msgstr "Opslaan"

#: includes/types/CMB2_Type_File.php:36 tests/test-cmb-types.php:683
#: tests/test-cmb-types.php:701
msgid "Add or Upload File"
msgstr "Voeg toe of upload bestand"

#: includes/types/CMB2_Type_File_List.php:36 tests/test-cmb-types.php:639
#: tests/test-cmb-types.php:663
msgid "Add or Upload Files"
msgstr "Voeg toe of upload bestanden"

#: includes/types/CMB2_Type_Taxonomy_Multicheck.php:27
#: includes/types/CMB2_Type_Taxonomy_Radio.php:25
msgid "No terms"
msgstr "Geen termen"

#. Plugin Name of the plugin/theme
msgid "CMB2"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://github.com/CMB2/CMB2"
msgstr ""

#. Description of the plugin/theme
msgid ""
"CMB2 will create metaboxes and forms with custom fields that will blow your "
"mind."
msgstr "CMB2 maakt te gekke metaboxen en formulieren met extra velden."

#. Author of the plugin/theme
msgid "WebDevStudios"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://webdevstudios.com"
msgstr ""

#: includes/CMB2_JS.php:109
msgctxt "Valid formatDate string for jquery-ui datepicker"
msgid "mm/dd/yy"
msgstr "mm/dd/jj"

#: includes/CMB2_JS.php:129
msgctxt ""
"Valid formatting string, as per "
"http://trentrichardson.com/examples/timepicker/"
msgid "hh:mm TT"
msgstr ""
