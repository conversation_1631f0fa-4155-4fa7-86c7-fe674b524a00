# Translation of Plugins - CMB2 - Development (trunk) in Finnish
# This file is distributed under the same license as the Plugins - CMB2 - Development (trunk) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2017-09-27 09:04:28+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: fi\n"
"Project-Id-Version: Plugins - CMB2 - Development (trunk)\n"

#. translators: %s: register_routes()
#: includes/shim/WP_REST_Controller.php:25
msgid "Method '%s' must be overridden."
msgstr ""

#: example-functions.php:732
msgid "%s &mdash; Updated!"
msgstr ""

#: includes/CMB2_Hookup_Base.php:42
msgid "%1$s should be implemented by the extended class."
msgstr ""

#. Author URI of the plugin/theme
msgid "https://cmb2.io"
msgstr ""

#. Author of the plugin/theme
msgid "CMB2 team"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://github.com/CMB2/CMB2"
msgstr ""

#: includes/CMB2_Types.php:412
msgid "Remove Row"
msgstr ""

#: includes/CMB2_Options_Hookup.php:131
msgid "Nothing to update."
msgstr ""

#: includes/CMB2_Options_Hookup.php:135
msgid "Settings updated."
msgstr ""

#: example-functions.php:665
msgid "Theme Options"
msgstr ""

#: includes/CMB2_hookup.php:458
msgid "Toggle panel: %s"
msgstr ""

#: includes/CMB2_Types.php:231
msgid "Custom CMB2 field type classes must extend CMB2_Type_Base."
msgstr ""

#: example-functions.php:783
msgid "Will show in REST API \"editable\" contexts only (`POST` requests)."
msgstr ""

#: example-functions.php:782
msgid "REST Editable Test Text"
msgstr ""

#: example-functions.php:776
msgid "Will show in the REST API for this box and for pages."
msgstr ""

#: example-functions.php:775
msgid "REST Test Text"
msgstr ""

#: example-functions.php:766
msgid "REST Test Box"
msgstr ""

#: includes/shim/WP_REST_Controller.php:308
msgid "Scope under which the request is made; determines fields present in response."
msgstr ""

#: includes/shim/WP_REST_Controller.php:290
msgid "Limit results to those matching a string."
msgstr ""

#: includes/shim/WP_REST_Controller.php:281
msgid "Maximum number of items to be returned in result set."
msgstr ""

#: includes/shim/WP_REST_Controller.php:273
msgid "Current page of the collection."
msgstr ""

#: includes/shim/WP_REST_Controller.php:35
#: includes/shim/WP_REST_Controller.php:47
#: includes/shim/WP_REST_Controller.php:59
#: includes/shim/WP_REST_Controller.php:71
#: includes/shim/WP_REST_Controller.php:83
#: includes/shim/WP_REST_Controller.php:95
#: includes/shim/WP_REST_Controller.php:107
#: includes/shim/WP_REST_Controller.php:119
#: includes/shim/WP_REST_Controller.php:131
#: includes/shim/WP_REST_Controller.php:143
#: includes/shim/WP_REST_Controller.php:155
#: includes/shim/WP_REST_Controller.php:168
msgid "Method '%s' not implemented. Must be overridden in subclass."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Fields.php:415
msgid "Value Error for %s"
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Fields.php:312
#: includes/rest-api/CMB2_REST_Controller_Fields.php:341
msgid "No field found by that id."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Fields.php:297
msgid "CMB2 Field value cannot be modified without the object_id and object_type parameters specified."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Fields.php:236
msgid "CMB2 Field value cannot be updated without the value parameter specified."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Fields.php:33
#: includes/rest-api/CMB2_REST_Controller_Fields.php:36
msgid "To view or modify the field's value, the 'object_id' and 'object_type' arguments are required."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Fields.php:30
msgid "When the '_rendered' argument is passed, the renderable field attributes will be returned fully rendered. By default, the names of the callback handers for the renderable attributes will be returned."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Fields.php:27
msgid "Includes the box object which the fields are registered to in the response."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Boxes.php:122
msgid "No boxes found."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Boxes.php:72
msgid "Includes the fully rendered attributes, 'form_open', 'form_close', as well as the enqueued 'js_dependencies' script handles, and 'css_dependencies' stylesheet handles."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller_Boxes.php:51
msgid "Includes the registered fields for the box in the response."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller.php:392
msgid "The title for the object."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller.php:385
msgid "The id for the object."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller.php:378
msgid "A human-readable description of the object."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller.php:300
msgid "No box found by that id. A box needs to be registered with the \"show_in_rest\" parameter configured."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller.php:277
msgid "This box does not have write permissions."
msgstr ""

#: includes/rest-api/CMB2_REST_Controller.php:257
msgid "This box does not have read permissions."
msgstr ""

#: includes/CMB2_Base.php:506 includes/types/CMB2_Type_Base.php:156
msgid "Invalid %1$s method: %2$s"
msgstr ""

#: includes/CMB2_Base.php:491 includes/CMB2_Hookup_Base.php:100
#: includes/CMB2_Options_Hookup.php:341 includes/types/CMB2_Type_Base.php:172
msgid "Invalid %1$s property: %2$s"
msgstr ""

#: includes/CMB2_Base.php:458
msgid "%1$s was called with a parameter that is <strong>deprecated</strong> since version %2$s with no alternative available."
msgstr ""

#: includes/CMB2_Base.php:456
msgid "%1$s was called with a parameter that is <strong>deprecated</strong> since version %2$s! %3$s"
msgstr ""

#: includes/CMB2_Base.php:426
msgid "Using the \"%1$s\" field parameter as a callback has been deprecated in favor of the \"%2$s\" parameter."
msgstr ""

#: includes/CMB2_Base.php:422
msgid "The \"%1$s\" field parameter has been deprecated in favor of the \"%2$s\" parameter."
msgstr ""

#. translators: %s: link to codex.wordpress.org/Embeds
#: example-functions.php:426
msgid "Enter a youtube, twitter, or instagram URL. Supports services listed at %s."
msgstr ""

#: example-functions.php:167
msgid "Hey there, I'm a read-only field"
msgstr ""

#: example-functions.php:163
msgid "Read-only Disabled Field"
msgstr ""

#. translators: 1: results for. 2: link to codex.wordpress.org/Embeds
#: includes/CMB2_Ajax.php:189 includes/helper-functions.php:105
msgid "No oEmbed Results Found for %1$s. View more info at %2$s."
msgstr ""

#: includes/CMB2_Field.php:1443
msgid "Sorry, this field does not have a cmb_id specified."
msgstr ""

#: example-functions.php:176
msgid "Custom Rendered Field"
msgstr ""

#: includes/CMB2_hookup.php:185
msgid "Term metaboxes configuration requires a \"taxonomies\" parameter."
msgstr ""

#: includes/CMB2_hookup.php:181
msgid "Term Metadata is a WordPress 4.4+ feature. Please upgrade your WordPress install."
msgstr ""

#: example-functions.php:646
msgid "Arbitrary Term Field"
msgstr ""

#: example-functions.php:639
msgid "Term Image"
msgstr ""

#: example-functions.php:624
msgid "Category Metabox"
msgstr ""

#. Description of the plugin/theme
msgid "CMB2 will create metaboxes and forms with custom fields that will blow your mind."
msgstr "CMB2 luo metaboxes ja muotojen mukautettuja kenttiä, joihin puhaltaa mieltäsi."

#. Plugin Name of the plugin/theme
msgid "CMB2"
msgstr ""

#: includes/helper-functions.php:303
msgid "Save"
msgstr "Tallenna"

#: includes/types/CMB2_Type_File.php:76
msgid "Add or Upload File"
msgstr "Lisää tai Lataa kuva"

#: includes/types/CMB2_Type_File_List.php:41
msgid "Add or Upload Files"
msgstr "Lisää tai lataa tiedostoja"

#: includes/types/CMB2_Type_Taxonomy_Base.php:115
msgid "No terms"
msgstr "Ei kohteita"

#: includes/CMB2_Types.php:349
msgid "Add Row"
msgstr "﻿Lisää rivi"

#: includes/CMB2_JS.php:230
msgid "Select / Deselect All"
msgstr "Valitse / Poista kaikki valinnat"

#: includes/CMB2_JS.php:229 includes/types/CMB2_Type_File_Base.php:82
msgid "Download"
msgstr "﻿Lataa"

#: includes/CMB2_Field_Display.php:432 includes/CMB2_JS.php:228
#: includes/types/CMB2_Type_File_Base.php:79
msgid "File:"
msgstr "﻿Tiedosto:"

#: includes/CMB2_JS.php:227 includes/CMB2_Types.php:412
#: includes/types/CMB2_Type_File_Base.php:84
msgid "Remove"
msgstr "Poista"

#: includes/CMB2_JS.php:226 includes/types/CMB2_Type_File_Base.php:64
msgid "Remove Image"
msgstr "﻿Poista kuva"

#: includes/CMB2_JS.php:225
msgid "Use these files"
msgstr ""

#: includes/CMB2_JS.php:224
msgid "Use this file"
msgstr "﻿Käytä tätä tiedostoa"

#: includes/CMB2_JS.php:218
msgctxt "Valid formatting string, as per http://trentrichardson.com/examples/timepicker/"
msgid "hh:mm TT"
msgstr "hh:mm TT"

#: includes/CMB2_JS.php:216
msgid "Now"
msgstr "Nyt"

#: includes/CMB2_JS.php:215
msgid "Second"
msgstr "Sekunti"

#: includes/CMB2_JS.php:214
msgid "Minute"
msgstr "Minuutti"

#: includes/CMB2_JS.php:213
msgid "Hour"
msgstr "Tunti"

#: includes/CMB2_JS.php:212
msgid "Time"
msgstr "Aika"

#: includes/CMB2_JS.php:211
msgid "Choose Time"
msgstr "Valitse aika"

#: includes/CMB2_JS.php:207 includes/CMB2_JS.php:217
msgid "Done"
msgstr "Valmis"

#: includes/CMB2_JS.php:206
msgid "Today"
msgstr "Tänään"

#: includes/CMB2_JS.php:205
msgid "Prev"
msgstr "Edellinen"

#: includes/CMB2_JS.php:204
msgid "Next"
msgstr "Seuraava"

#: includes/CMB2_JS.php:203
msgid "Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec"
msgstr "Tam, Hel, Mar, Huh, Tou, Kes, Hei, Elo, Syy, Lok, Mar, Jou"

#: includes/CMB2_JS.php:202
msgid "January, February, March, April, May, June, July, August, September, October, November, December"
msgstr "Tammikuu, Helmikuu, Maaliskuu, Huhtikuu, Toukokuu, Kesäkuu, Heinäkuu, Elokuu, Syyskuu, Lokakuu, Marraskuu, Joulukuu"

#: includes/CMB2_JS.php:201
msgid "Sun, Mon, Tue, Wed, Thu, Fri, Sat"
msgstr "Sun, Man, Tii, Kes, Tor, Per, Lau"

#: includes/CMB2_JS.php:200
msgid "Su, Mo, Tu, We, Th, Fr, Sa"
msgstr "Su, Ma, Ti, Ke, To, Pe, La"

#: includes/CMB2_JS.php:199
msgid "Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday"
msgstr "Sunnuntai, Maanantai, Tiistai, Keskiviikko, Torstai, Perjantai, Lauantai"

#: includes/CMB2_JS.php:198
msgctxt "Valid formatDate string for jquery-ui datepicker"
msgid "mm/dd/yy"
msgstr "kk/pp/vv"

#: includes/CMB2_JS.php:168
msgid "Current Color"
msgstr "Nykyinen väri"

#: includes/CMB2_JS.php:167
msgid "Select Color"
msgstr "﻿Valitse väri"

#: includes/CMB2_JS.php:166
msgid "Default"
msgstr "Oletusarvo"

#: includes/CMB2_JS.php:165 includes/CMB2_JS.php:208
msgid "Clear"
msgstr "﻿Tyhjennä"

#: includes/CMB2_Field.php:1328 includes/CMB2_Field.php:1332
msgid "None"
msgstr "﻿Ei mitään"

#: includes/CMB2_Field.php:1297
msgid "Remove Group"
msgstr "Poista Ryhmä"

#: includes/CMB2_Field.php:1296
msgid "Add Group"
msgstr "Lisää Ryhmä"

#: includes/CMB2_Ajax.php:181
msgid "Remove Embed"
msgstr "Poista Embed"

#: includes/CMB2_Ajax.php:75
msgid "Please Try Again"
msgstr "Yritä uudelleen"

#: includes/CMB2.php:582
msgid "Click to toggle"
msgstr "Klikkaa vaihtaaksesi"

#: includes/CMB2.php:179
msgid "Metabox configuration is required to have an ID parameter."
msgstr ""

#: example-functions.php:695
msgid "Site Background Color"
msgstr "Sivuston taustaväri"

#: example-functions.php:604
msgid "User Field"
msgstr "Käyttäjäkenttä"

#: example-functions.php:597
msgid "Linkedin URL"
msgstr "Linkedin URL"

#: example-functions.php:590
msgid "Google+ URL"
msgstr "Google+ URL"

#: example-functions.php:583
msgid "Twitter URL"
msgstr "Twitter URL"

#: example-functions.php:576
msgid "Facebook URL"
msgstr "Facebook URL"

#: example-functions.php:569
msgid "Avatar"
msgstr "Avatar"

#: example-functions.php:561 example-functions.php:631
msgid "Extra Info"
msgstr "Lisätiedot"

#: example-functions.php:554
msgid "User Profile Metabox"
msgstr "Käyttäjän profiili Metabox"

#: example-functions.php:535
msgid "Image Caption"
msgstr "Ote kuvasta"

#: example-functions.php:529
msgid "Entry Image"
msgstr ""

#: example-functions.php:523
msgid "Write a short description for this entry"
msgstr ""

#: example-functions.php:522
msgid "Description"
msgstr "Kuvaus:"

#: example-functions.php:515
msgid "Entry Title"
msgstr ""

#: example-functions.php:502
msgid "Remove Entry"
msgstr "Poista merkintä"

#: example-functions.php:501
msgid "Add Another Entry"
msgstr "Lisää merkintä"

#: example-functions.php:500
msgid "Entry {#}"
msgstr "Merkintä {#}"

#: example-functions.php:498
msgid "Generates reusable form entries"
msgstr "Luo uudelleenkäytettäviä lomakemerkintöjä"

#: example-functions.php:490
msgid "Repeating Field Group"
msgstr "Toistuva kenttä"

#: example-functions.php:459
msgid "About Page Metabox"
msgstr "Tietoja-sivun metabox"

#: example-functions.php:423
msgid "oEmbed"
msgstr "oEmbed"

#: example-functions.php:416
msgid "Upload or add multiple images/attachments."
msgstr "Lataa tai lisätä useita kuvia ja liitteitä."

#: example-functions.php:415
msgid "Multiple Files"
msgstr "Useita tiedostoja"

#: example-functions.php:409
msgid "Upload an image or enter a URL."
msgstr "Lataa kuva tai kirjoita URL-osoite."

#: example-functions.php:408
msgid "Test Image"
msgstr "Testi kuva"

#: example-functions.php:398
msgid "Test wysiwyg"
msgstr "Testi wysiwyg"

#: example-functions.php:392
msgid "Check Three"
msgstr "Tarkista kolme"

#: example-functions.php:391
msgid "Check Two"
msgstr "Tarkista kaksi"

#: example-functions.php:390
msgid "Check One"
msgstr "Tarkista yksi"

#: example-functions.php:384
msgid "Test Multi Checkbox"
msgstr "Testi Multi valintaruutu"

#: example-functions.php:377
msgid "Test Checkbox"
msgstr "Testaa valintaruudun valinta"

#: example-functions.php:368
msgid "Test Taxonomy Multi Checkbox"
msgstr "Testi taksonomia Multi valintaruutu"

#: example-functions.php:360
msgid "Test Taxonomy Select"
msgstr "Testi taksonomia Valitse"

#: example-functions.php:351
msgid "Test Taxonomy Radio"
msgstr "Testi taksonomia Radio"

#: example-functions.php:339
msgid "Test Radio"
msgstr "Testaa Radio"

#: example-functions.php:326
msgid "Test Radio inline"
msgstr "Testi Radio inline"

#: example-functions.php:321 example-functions.php:334
#: example-functions.php:346
msgid "Option Three"
msgstr "Kolmas vaihtoehto"

#: example-functions.php:320 example-functions.php:333
#: example-functions.php:345
msgid "Option Two"
msgstr "Vaihtoehdossa"

#: example-functions.php:319 example-functions.php:332
#: example-functions.php:344
msgid "Option One"
msgstr "Ensimmäinen vaihtoehto"

#: example-functions.php:313
msgid "Test Select"
msgstr "Testaa Valitse"

#: example-functions.php:307
msgid "This is a title description"
msgstr "Tämä on otsikon kuvaus"

#: example-functions.php:306
msgid "Test Title Weeeee"
msgstr "Testi otsikko Weeeee"

#: example-functions.php:288
msgid "Test Text Area for Code"
msgstr "Testaa teksti-alueen koodi"

#: example-functions.php:281
msgid "Test Text Area Small"
msgstr "Testaa teksti alue pieni"

#: example-functions.php:274
msgid "Test Text Area"
msgstr "Testaa tekstialueen"

#: example-functions.php:258
msgid "Test Color Picker"
msgstr "Testissä Color Picker"

#: example-functions.php:249
msgid "Test Money"
msgstr "Testaa rahaa"

#: example-functions.php:232
msgid "Test Date/Time Picker Combo (UNIX timestamp)"
msgstr "Testin päivämäärä ja aika Picker Combo (UNIX timestamp)"

#: example-functions.php:224
msgid "Test Date Picker (UNIX timestamp)"
msgstr "Testata päivämäärävalitsinta (UNIX timestamp)"

#: example-functions.php:216
msgid "Test Date Picker"
msgstr "Testi päivämäärävalitsin"

#: example-functions.php:209 example-functions.php:210
msgid "Time zone"
msgstr "Aikavyöhyke"

#: example-functions.php:201
msgid "Test Time"
msgstr "Testin aikana"

#: example-functions.php:193
msgid "Test Text Email"
msgstr "Testaa teksti sähköposti"

#: example-functions.php:184
msgid "Website URL"
msgstr "verkkosivusto"

#: example-functions.php:156
msgid "Test Text Medium"
msgstr "Testiväliaineena teksti"

#: example-functions.php:143
msgid "Test Text Small"
msgstr "Testaa teksti pieni"

#: example-functions.php:131 example-functions.php:144
#: example-functions.php:157 example-functions.php:164
#: example-functions.php:177 example-functions.php:185
#: example-functions.php:194 example-functions.php:202
#: example-functions.php:217 example-functions.php:225
#: example-functions.php:233 example-functions.php:250
#: example-functions.php:259 example-functions.php:275
#: example-functions.php:282 example-functions.php:289
#: example-functions.php:314 example-functions.php:327
#: example-functions.php:340 example-functions.php:352
#: example-functions.php:361 example-functions.php:369
#: example-functions.php:378 example-functions.php:385
#: example-functions.php:399 example-functions.php:471
#: example-functions.php:562 example-functions.php:570
#: example-functions.php:577 example-functions.php:584
#: example-functions.php:591 example-functions.php:598
#: example-functions.php:605 example-functions.php:632
#: example-functions.php:640 example-functions.php:647
#: example-functions.php:696
msgid "field description (optional)"
msgstr "kenttä kuvaus (valinnainen)"

#: example-functions.php:130 example-functions.php:470
msgid "Test Text"
msgstr "Testata tekstin"

#: example-functions.php:117
msgid "Test Metabox"
msgstr "Testaa Metabox"