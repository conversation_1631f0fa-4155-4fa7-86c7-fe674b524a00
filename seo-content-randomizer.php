<?php
/**
 * @wordpress-plugin
 * Plugin Name:       Massive Marketing SEO-Content-Randomizer
 * Plugin URI:        Massive Marketing SEO
 * Description:       Massive Marketing SEO.
 * Version:           1.0.0
 * Author:            Massive Marketing SEO
 * Author URI:        Massive Marketing SEO
 * License:           GPL-2.0+
 * License URI:       Massive Marketing SEO
 * Text Domain:       issscr
 * Domain Path:       /languages
 */

// Define constants for full enterprise features
define('ISSSCR_VERSION', '1.0.0');
define('ISSSCR_BASENAME', plugin_basename(__FILE__));
define('ISSSCR_WHITELABEL', true);
define('ISSSCR_ACTIVATE_ADDON_FEATURES', true);
define('ISSSCR_PLAN', 'enterprise'); // Force enterprise mode

// Security check
if (!defined('WPINC')) {
    die;
}

// Mock Freemius functions to bypass license checks
if (!function_exists('issscr_fs')) {
    function issscr_fs() {
        return (object) [
            'is_paying' => function() { return true; },
            'is_plan' => function($plan) { return true; },
            'is_plan_or_trial' => function($plan) { return true; },
            'can_use_premium_code' => function() { return true; },
            'get_upgrade_url' => function() { return '#'; },
            'add_action' => function() { return true; },
            'add_filter' => function() { return true; }
        ];
    }
}

// Activation/Deactivation hooks (unchanged)
function activate_issscr() {
    require_once plugin_dir_path(__FILE__) . 'includes/class-issscr-activator.php';
    ISSSCR_Activator::activate();
}
register_activation_hook(__FILE__, 'activate_issscr');

function deactivate_issscr() {
    require_once plugin_dir_path(__FILE__) . 'includes/class-issscr-deactivator.php';
    ISSSCR_Deactivator::deactivate();
}
register_deactivation_hook(__FILE__, 'deactivate_issscr');

// Updater functionality (unchanged)
function update_issscr() {
    require_once plugin_dir_path(__FILE__) . 'includes/class-issscr-updater.php';
    new ISSSCR_Updater();
}
add_action('init', 'update_issscr', 100, 0);

// Load core plugin class (unchanged)
require plugin_dir_path(__FILE__) . 'includes/class-issscr.php';

// Run the plugin (unchanged)
function run_issscr() {
    $plugin = new ISSSCR();
    $plugin->run();
}
run_issscr();